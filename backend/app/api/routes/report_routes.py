from os import error
from typing import Optional
from app.api.routes.assessment_routes import ReportRequest
from app.models.assessment_manager import assessment_report_by_topic, assessment_report_by_user, assessment_report_with_question_stats
from app.models.report_manager import get_skillwise_heatmap_data
from app.utils import rate_limiter
from app.utils.api_response import error_response, success_response
from fastapi import  Depends,APIRouter, status

# Create router for assessment-related endpoints
assessment_router = APIRouter()
skill_router = APIRouter()

# =============================================================================
#  assessements reports
# =============================================================================

@assessment_router.post("/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user or topic"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(request.user_name, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(request.report_topic, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            base_report, score_report = assessment_report_with_question_stats(
                request.assessment_base_name, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )
        
        
# =============================================================================
# skills reports
# =============================================================================
       
@skill_router.get("/admin/reports/skillwise-heatmap")
async def get_skillwise_heatmap():
    """
    Get skill performance data for all users in a format suitable for a heatmap

    Returns a matrix of user-skill performance data with accuracy percentages
    """
    heatmap_data = get_skillwise_heatmap_data()

    if heatmap_data is None:
        return error_response(
            message="Error fetching skillwise heatmap data",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )

    return success_response(
        data=heatmap_data,
        message="Skillwise heatmap data retrieved successfully",
    )        
    
# =============================================================================
# session reports
# =============================================================================