"""
Performance Calculation Utility Functions

This module provides reusable performance calculation utilities
to reduce code duplication and improve maintainability.
"""

import os
from typing import Dict, List, Optional

from .db_utils import get_multiple_rows


def calculate_question_score(level: str, result: str) -> int:
    """
    Calculate score for a single question based on difficulty level and result.

    Args:
        level: Question difficulty level (easy, intermediate, advanced)
        result: Question result (correct, incorrect, timeout)

    Returns:
        Score for the question
    """
    if result.lower() in ("timeout", "incorrect"):
        return 0

    score_mapping = {"easy": 1, "intermediate": 2, "advanced": 3}

    return score_mapping.get(level.lower(), 0)


def get_max_possible_score() -> int:
    """
    Calculate maximum possible score based on question configuration.

    Returns:
        Maximum possible score
    """
    # Get question counts for each difficulty level from environment variables
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

    # Max score calculation: easy(1) + intermediate(2) + advanced(3) points per question
    max_score = (easy_count * 1) + (intermediate_count * 2) + (advanced_count * 3)

    return max_score


def calculate_performance_percentage(obtained_score: int, max_score: Optional[int] = None) -> float:
    """
    Calculate performance percentage.

    Args:
        obtained_score: Score obtained by the user
        max_score: Maximum possible score (calculated if not provided)

    Returns:
        Performance percentage
    """
    if max_score is None:
        max_score = get_max_possible_score()

    if max_score == 0:
        return 0.0

    return (obtained_score / max_score) * 100


def get_performance_level(obtained_score, total_score):
    """
    Determine the performance level based on the obtained score percentage.

    Args:
        obtained_score (int): The score obtained by the user.
        total_score (int): The maximum possible score.

    Returns:
        str: The performance level (Fail, Basic, Acceptable, Exceed Expectation, OUTSTANDING).
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    # Corrected logic for level determination
    # Handles the case where total_score might be 0 or obtained_score is 0
    if percentage == 0 and obtained_score == 0:
        return "Fail"

    performance = "Fail"  # Default
    for threshold, level in levels:
        if percentage >= threshold:  # Check if percentage is greater than or equal to threshold
            performance = level
        else:  # if it's less, then the previous level was correct
            break

    # Special case for 100%
    if percentage == 100:
        performance = "OUTSTANDING"

    return performance



def calculate_accuracy_percentage(correct_answers: int, total_questions: int) -> float:
    """
    Calculate accuracy percentage.

    Args:
        correct_answers: Number of correct answers
        total_questions: Total number of questions attempted

    Returns:
        Accuracy percentage
    """
    if total_questions == 0:
        return 0.0

    return (correct_answers / total_questions) * 100


def get_skill_performance_data(user_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Get skill performance data for a user.

    Args:
        user_id: User ID to get performance for
        assessment_id: Optional assessment ID to filter by

    Returns:
        List of skill performance dictionaries
    """
    query = """
        SELECT
            s.name as skill_name,
            COUNT(ua.id) as total_questions,
            SUM(CASE WHEN ua.is_correct = true THEN 1 ELSE 0 END) as correct_answers,
            AVG(CASE WHEN ua.is_correct = true THEN 1.0 ELSE 0.0 END) * 100 as accuracy_percentage,
            SUM(ua.score) as total_score
        FROM user_answers ua
        JOIN sessions sess ON ua.session_id = sess.id
        JOIN users u ON sess.user_id = u.id
        JOIN assessments a ON sess.assessment_id = a.id
        JOIN assessment_skills aas ON a.id = aas.assessment_id
        JOIN skills s ON aas.skill_id = s.id
        WHERE u.external_id = %s
    """

    params = [user_id]

    if assessment_id:
        query += " AND a.id = %s"
        params.append(assessment_id)

    query += """
        GROUP BY s.id, s.name
        ORDER BY s.name
    """

    results = get_multiple_rows(query, tuple(params), as_dict=True)

    # Process results to ensure proper data types
    processed_results = []
    for result in results:
        processed_results.append(
            {
                "skill_name": result["skill_name"],
                "total_questions": int(result["total_questions"]),
                "correct_answers": int(result["correct_answers"]),
                "accuracy_percentage": float(result["accuracy_percentage"]) if result["accuracy_percentage"] else 0.0,
                "total_score": float(result["total_score"]) if result["total_score"] else 0.0,
            }
        )

    return processed_results


def get_overall_performance_summary(user_id: str, assessment_id: Optional[int] = None) -> Dict:
    """
    Get overall performance summary for a user.

    Args:
        user_id: User ID to get summary for
        assessment_id: Optional assessment ID to filter by

    Returns:
        Dictionary containing overall performance summary
    """
    query = """
        SELECT
            COUNT(ua.id) as total_questions,
            SUM(CASE WHEN ua.is_correct = true THEN 1 ELSE 0 END) as correct_answers,
            SUM(ua.score) as total_score,
            AVG(ua.time_taken) as average_time_taken
        FROM user_answers ua
        JOIN sessions sess ON ua.session_id = sess.id
        JOIN users u ON sess.user_id = u.id
        WHERE u.external_id = %s
    """

    params = [user_id]

    if assessment_id:
        query += " AND sess.assessment_id = %s"
        params.append(assessment_id)

    result = get_multiple_rows(query, tuple(params), as_dict=True)

    if not result:
        return {
            "total_questions": 0,
            "correct_answers": 0,
            "total_score": 0.0,
            "accuracy_percentage": 0.0,
            "performance_level": "No Data",
            "average_time_taken": 0.0,
        }

    data = result[0]
    total_questions = int(data["total_questions"])
    correct_answers = int(data["correct_answers"])
    total_score = float(data["total_score"]) if data["total_score"] else 0.0

    return {
        "total_questions": total_questions,
        "correct_answers": correct_answers,
        "total_score": total_score,
        "accuracy_percentage": calculate_accuracy_percentage(correct_answers, total_questions),
        "performance_level": get_performance_level(int(total_score)),
        "average_time_taken": float(data["average_time_taken"]) if data["average_time_taken"] else 0.0,
    }


def get_difficulty_wise_performance(user_id: str, assessment_id: Optional[int] = None) -> Dict:
    """
    Get difficulty-wise performance breakdown for a user.

    Args:
        user_id: User ID to get performance for
        assessment_id: Optional assessment ID to filter by

    Returns:
        Dictionary containing difficulty-wise performance
    """
    query = """
        SELECT
            q.level as difficulty,
            COUNT(ua.id) as total_questions,
            SUM(CASE WHEN ua.is_correct = true THEN 1 ELSE 0 END) as correct_answers,
            SUM(ua.score) as total_score
        FROM user_answers ua
        JOIN sessions sess ON ua.session_id = sess.id
        JOIN users u ON sess.user_id = u.id
        JOIN questions q ON ua.question_id = q.que_id
        WHERE u.external_id = %s
    """

    params = [user_id]

    if assessment_id:
        query += " AND sess.assessment_id = %s"
        params.append(assessment_id)

    query += """
        GROUP BY q.level
        ORDER BY q.level
    """

    results = get_multiple_rows(query, tuple(params), as_dict=True)

    # Process results into a structured format
    difficulty_performance = {}
    for result in results:
        difficulty = result["difficulty"]
        total_questions = int(result["total_questions"])
        correct_answers = int(result["correct_answers"])

        difficulty_performance[difficulty] = {
            "total_questions": total_questions,
            "correct_answers": correct_answers,
            "total_score": float(result["total_score"]) if result["total_score"] else 0.0,
            "accuracy_percentage": calculate_accuracy_percentage(correct_answers, total_questions),
        }

    return difficulty_performance


def get_weakest_skill(user_id: str, assessment_id: Optional[int] = None) -> Optional[Dict]:
    """
    Get the weakest skill for a user based on accuracy percentage.

    Args:
        user_id: User ID to analyze
        assessment_id: Optional assessment ID to filter by

    Returns:
        Dictionary containing weakest skill data or None if no data
    """
    skill_performance = get_skill_performance_data(user_id, assessment_id)

    if not skill_performance:
        return None

    # Find skill with lowest accuracy percentage
    weakest_skill = min(skill_performance, key=lambda x: x["accuracy_percentage"])

    return weakest_skill


def get_strongest_skill(user_id: str, assessment_id: Optional[int] = None) -> Optional[Dict]:
    """
    Get the strongest skill for a user based on accuracy percentage.

    Args:
        user_id: User ID to analyze
        assessment_id: Optional assessment ID to filter by

    Returns:
        Dictionary containing strongest skill data or None if no data
    """
    skill_performance = get_skill_performance_data(user_id, assessment_id)

    if not skill_performance:
        return None

    # Find skill with highest accuracy percentage
    strongest_skill = max(skill_performance, key=lambda x: x["accuracy_percentage"])

    return strongest_skill


def calculate_improvement_suggestions(user_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Calculate improvement suggestions based on performance data.

    Args:
        user_id: User ID to analyze
        assessment_id: Optional assessment ID to filter by

    Returns:
        List of improvement suggestions
    """
    suggestions = []

    # Get performance data
    overall_performance = get_overall_performance_summary(user_id, assessment_id)
    skill_performance = get_skill_performance_data(user_id, assessment_id)
    difficulty_performance = get_difficulty_wise_performance(user_id, assessment_id)

    # Overall accuracy suggestions
    if overall_performance["accuracy_percentage"] < 60:
        suggestions.append(
            {
                "type": "overall_accuracy",
                "priority": "high",
                "message": "Your overall accuracy is below 60%. Focus on understanding fundamental concepts.",
            }
        )

    # Skill-specific suggestions
    for skill in skill_performance:
        if skill["accuracy_percentage"] < 50:
            suggestions.append(
                {
                    "type": "skill_weakness",
                    "priority": "high",
                    "skill_name": skill["skill_name"],
                    "message": f"Your performance in {skill['skill_name']} is {skill['accuracy_percentage']:.1f}%. This skill requires immediate attention.",
                }
            )

    # Difficulty-specific suggestions
    for difficulty, data in difficulty_performance.items():
        if data["accuracy_percentage"] < 40:
            suggestions.append(
                {
                    "type": "difficulty_weakness",
                    "priority": "medium",
                    "difficulty": difficulty,
                    "message": f"You're struggling with {difficulty} questions ({data['accuracy_percentage']:.1f}% accuracy). Consider additional practice.",
                }
            )

    return suggestions
