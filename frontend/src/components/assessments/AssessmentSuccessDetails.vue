<template>
  <section
    class="bg-green-900/20 backdrop-blur-sm border border-green-500/30 rounded-xl p-8 text-center"
  >
    <h2
      class="text-xl font-semibold text-green-400 mb-6 flex items-center justify-center"
    >
      <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clip-rule="evenodd"
        />
      </svg>
      Assessment Created Successfully!
    </h2>

    <!-- Action Button -->
    <div class="flex justify-center mt-6">
      <button class="btn-phantom text-base py-3 px-6" @click="createSession">
        <span class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z"
            />
          </svg>
          Generate Sessions
        </span>
      </button>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Success Details Component
 * Shows success information and next steps after assessment creation
 */
import { useNavigation } from "@/composables";
import { logUserAction } from "@/utils/logger";

const props = defineProps({
  assessment: {
    type: Object,
    required: true,
  },
});

const navigation = useNavigation();

// Action handlers
// Commented out unused functions to fix linting errors
// const viewAssessment = () => {
//   logUserAction("view_created_assessment", {
//     assessmentId: props.assessment.assessment_id,
//   });
//   navigation.navigateTo(
//     `/assessment-details/${props.assessment.assessment_id}`,
//   );
// };

// const addFixedQuestions = () => {
//   logUserAction("add_fixed_questions", {
//     assessmentId: props.assessment.assessment_id,
//   });
//   navigation.navigateTo(
//     `/assessment-details/${props.assessment.assessment_id}?tab=questions`,
//   );
// };

const createSession = () => {
  logUserAction("create_session_from_success", {
    assessmentId: props.assessment.assessment_id,
  });
  navigation.navigateTo(
    `/create-session?assessment_id=${props.assessment.assessment_id}`,
  );
};
</script>

<style scoped>
.btn-phantom {
  @apply bg-gradient-to-r from-phantom-blue to-phantom-indigo text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-phantom-blue/25;
}

.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg transition-all duration-200 hover:bg-white/10 hover:border-white/20;
}
</style>
